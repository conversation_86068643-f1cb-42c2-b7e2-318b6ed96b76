<script lang="ts" setup>
import DataTable, { type DataTableProps } from 'primevue/datatable'

interface Props extends DataTableProps {
  emptyText?: string
}

const { rows, totalRecords, emptyText = '没有数据', ...props } = defineProps<Props>()
const currentPage = defineModel<number>('currentPage', { default: 1 })
const emit = defineEmits(['update:currentPage'])
</script>

<template>
  <DataTable
    v-bind="props"
    tableStyle="min-width: 50rem"
    :showHeaders="value?.length !== 0"
    paginator
    lazy
    :first="(currentPage - 1) * (rows ?? 0)"
    @page="
      ({ page }) => {
        emit('update:currentPage', page + 1)
      }
    "
    :rows="rows"
    :totalRecords="totalRecords"
  >
    <slot />
    <template #empty>
      <div class="flex min-h-24 items-center justify-center">
        {{ props.value?.length === 0 ? emptyText : '加载中...' }}
      </div>
    </template>
  </DataTable>
</template>
