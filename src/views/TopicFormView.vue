<script lang="ts" setup>
import type { FormSubmitEvent } from '@primevue/forms'
import { Form, FormField, FormFieldMessage } from '@/components/primevue'
import { InputText, Button } from 'primevue'
import { ref } from 'vue'
import { zodResolver } from '@primevue/forms/resolvers/zod'
import { z } from 'zod'

const schema = z.object({
  comment: z.string().min(1, { message: '请输入描述' }),
})
type Schema = z.infer<typeof schema>

interface Props {
  initialValues?: Schema
}
withDefaults(defineProps<Props>(), { initialValues: () => ({ comment: '' }) })
const emit = defineEmits<{
  submit: [values: Values]
  cancel: []
}>()

interface Values {
  comment: string
}

const resolver = ref(zodResolver(schema))

function handleSubmit(event: FormSubmitEvent) {
  if (!event.valid) return
  emit('submit', event.values as Values)
}
</script>

<template>
  <Form :initialValues :resolver class="flex w-full flex-col gap-4" @submit="handleSubmit">
    <FormField v-slot="$field" name="comment">
      <InputText placeholder="描述" fluid />
      <FormFieldMessage v-if="$field?.invalid">{{ $field.error?.message }}</FormFieldMessage>
    </FormField>
    <div class="flex justify-end gap-2">
      <Button type="button" label="取消" severity="secondary" @click="emit('cancel')"></Button>
      <Button type="submit" label="保存" />
    </div>
  </Form>
</template>
