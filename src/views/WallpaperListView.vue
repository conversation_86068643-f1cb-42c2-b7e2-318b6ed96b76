<script setup lang="tsx">
import { api } from '@/api'
import type {
  DashTopicsPost200Response,
  DashWallpapersGet200Response,
  DashWallpapersGet200ResponseWallpapersInner,
} from '@/api-client'
import { DashWallpapersGetSizedForEnum } from '@/api-client'
import PagingTable from '@/components/PagingTable.vue'
import { RouteName } from '@/router'
import { Column, Tag, Dialog, DataTable, Button, Image, Select } from 'primevue'
import { useDialog } from 'primevue/usedialog'
import { reactive, ref, watch } from 'vue'
import WallpaperJoinTopicsView from './WallpaperJoinTopicsView.vue'
import ViewLayout from '@/layout/ViewLayout.vue'

const dialog = useDialog()

const tableLoading = ref(true)
const viewData = ref<DashWallpapersGet200Response>()

const filters = reactive<{ sizedFor?: DashWallpapersGetSizedForEnum }>({
  sizedFor: undefined,
})
watch(filters, () => {
  updateViewData()
})

const currentPage = ref(1)
function updateViewData() {
  tableLoading.value = true
  return api
    .dashWallpapersGet(
      currentPage.value,
      20,
      [
        'wallpapers.id',
        'wallpapers.images',
        'wallpapers.related_topic_count',
        'current_page',
        'current_page_size',
        'total',
      ],
      filters.sizedFor,
    )
    .then((res) => {
      viewData.value = res.data
    })
    .finally(() => {
      tableLoading.value = false
    })
}
watch(
  currentPage,
  () => {
    updateViewData()
  },
  { immediate: true },
)

const dialogVisiable = ref(false)
const dialogTableValue = ref<DashTopicsPost200Response[]>()
const dialogTableLoading = ref(true)
function handleJoinInTopic(data: DashWallpapersGet200ResponseWallpapersInner) {
  const vnode = <WallpaperJoinTopicsView wallpaper={data} onUpdate={() => updateViewData()} />
  dialog.open(vnode, {
    props: { header: '添加到专题', modal: true },
  })
}
</script>

<template>
  <ViewLayout>
    <template #header>
      <Select
        v-model="filters.sizedFor"
        size="small"
        :options="Object.values(DashWallpapersGetSizedForEnum)"
        show-clear
        placeholder="壁纸适用屏幕"
      ></Select>
    </template>
    <PagingTable
      v-model:current-page="currentPage"
      :loading="tableLoading"
      :value="viewData?.wallpapers"
      :rows="viewData?.current_page_size"
      :total-records="viewData?.total"
      data-key="id"
      scrollable
      scrollHeight="flex"
    >
      <Column header="图片">
        <template #body="slotProps: { data: DashWallpapersGet200ResponseWallpapersInner }">
          <Image
            image-class="size-25 object-contain"
            :src="slotProps.data.images!.default"
            preview
          />
        </template>
      </Column>
      <Column header="已关联专题">
        <template #body="slotProps: { data: DashWallpapersGet200ResponseWallpapersInner }">
          <template v-if="slotProps.data.related_topic_count === 0">
            <Tag severity="secondary">0</Tag>
          </template>
          <button
            v-else
            class="cursor-pointer"
            @click="
            () => {
              if (slotProps.data.related_topic_count === 0) return

              dialogVisiable = true
              dialogTableLoading = true
              api
                .dashWallpapersWallpaperIdTopicsGet(slotProps.data.id!)
                .then((resp) => {
                  dialogTableValue = resp.data.topics
                })
                .finally(() => {
                  dialogTableLoading = false
                })
            }
          "
          >
            <Tag>{{ slotProps.data.related_topic_count }}</Tag>
          </button>
        </template>
      </Column>
      <Column>
        <template #body="slotProps: { data: DashWallpapersGet200ResponseWallpapersInner }">
          <Button
            icon="pi pi-plus"
            rounded
            size="small"
            @click="handleJoinInTopic(slotProps.data)"
          />
        </template>
      </Column>
    </PagingTable>
  </ViewLayout>

  <Dialog
    v-model:visible="dialogVisiable"
    header="关联专题"
    class="min-w-200"
    @after-hide="
      () => {
        dialogTableValue = undefined
      }
    "
  >
    <DataTable :value="dialogTableValue" :loading="dialogTableLoading">
      <Column header="专题备注" field="comment"></Column>
      <Column>
        <template #body="slotProps: { data: DashTopicsPost200Response }">
          <Button v-slot="slot" asChild variant="link">
            <RouterLink
              :class="slot.class"
              :to="{ name: RouteName.TopicWallpapers, params: { id: slotProps.data.id } }"
            >
              前往专题
            </RouterLink>
          </Button>
        </template>
      </Column>
    </DataTable>
  </Dialog>
</template>
