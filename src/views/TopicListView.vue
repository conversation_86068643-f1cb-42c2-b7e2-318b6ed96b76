<script lang="tsx" setup>
import Column from 'primevue/column'
import { api } from '@/api'
import { onMounted, ref, watch } from 'vue'
import But<PERSON> from 'primevue/button'
import Dialog from 'primevue/dialog'
import { useDialog, useToast } from 'primevue'
import PagingTable from '@/components/PagingTable.vue'
import { RouteName } from '@/router'
import { useConfirm } from '@/components/primevue'
import TopicPublishFormView from '@/views/TopicPublishFormView.vue'
import type { DashTopicsGet200Response, DashTopicsGet200ResponseTopicsInner } from '@/api-client'
import type { AxiosError } from 'axios'
import TopicFormView from './TopicFormView.vue'
import { useAddingTopicForm } from './hooks'
import { Tag } from 'primevue'
import ViewLayout from '@/layout/ViewLayout.vue'

const confirm = useConfirm()
const toast = useToast()

const viewData = ref<DashTopicsGet200Response>()
const tableLoading = ref(true)

const currentPage = ref(1)
async function updateViewData() {
  tableLoading.value = true
  api
    .dashTopicsGet(currentPage.value, 15, undefined, [
      'topics.id',
      'topics.comment',
      'topics.wallpaper_count',
      'topics.published_count',
      'total',
      'current_page',
      'current_page_size',
    ])
    .then((resp) => {
      viewData.value = resp.data
    })
    .finally(() => {
      tableLoading.value = false
    })
}
watch(currentPage, () => {
  updateViewData()
})
onMounted(() => {
  updateViewData()
})

const publishDialogVisible = ref(false)
const currentOperatingTopic = ref<DashTopicsGet200ResponseTopicsInner>()

const dialog = useDialog()
const addingTopicForm = useAddingTopicForm()

function handleClickCreation() {
  addingTopicForm.open({
    onSuccess() {
      if (currentPage.value === 1) {
        updateViewData()
      } else {
        currentPage.value = 1
      }
    },
  })
}

function handleClickEditing(data: DashTopicsGet200ResponseTopicsInner) {
  const dialogInstance = dialog.open(
    <TopicFormView
      initialValues={{ comment: data.comment! }}
      onSubmit={(values) => {
        api.dashTopicsTopicIdPut(data.id!, values).then(() => {
          updateViewData()
          dialogInstance.close()
        })
      }}
      onCancel={() => {
        dialogInstance.close()
      }}
    />,
    {
      props: {
        header: '编辑专题',
        modal: true,
        style: { minWidth: '30rem' },
      },
    },
  )
}

function handlePublishTopic(values: { clientId: string; title: string }) {
  if (!currentOperatingTopic.value) return
  api
    .dashTopicTopicIdPublishClientIdPost(
      currentOperatingTopic.value.id!,
      values.clientId,
      { title: values.title },
      { skipCheckStatusCodes: [409] },
    )
    .catch((err: AxiosError) => {
      if (err.response?.status === 409) {
        toast.add({
          severity: 'error',
          summary: '发布失败',
          detail: '该专题已发布到该客户端',
          life: 3000,
        })
      } else {
        return Promise.reject(err)
      }
    })
  updateViewData()
  publishDialogVisible.value = false
}
</script>

<template>
  <ViewLayout>
    <template #header>
      <div class="flex justify-end">
        <Button icon="pi pi-plus" label="新增专题" @click="handleClickCreation"></Button>
      </div>
    </template>
    <PagingTable
      :value="viewData?.topics"
      v-model:currentPage="currentPage"
      :rows="viewData?.current_page_size"
      :totalRecords="viewData?.total"
      :loading="tableLoading"
      scrollable
      scrollHeight="flex"
    >
      <Column field="id" header="ID"></Column>
      <Column field="comment" header="描述"></Column>
      <Column header="壁纸">
        <template #body="slotProps: { data: DashTopicsGet200ResponseTopicsInner }">
          <RouterLink
            class="underline"
            :to="{ name: RouteName.TopicWallpapers, params: { id: slotProps.data.id } }"
          >
            {{ slotProps.data.wallpaper_count }} 张
          </RouterLink>
        </template>
      </Column>
      <Column header="已发布">
        <template #body="slotProps: { data: DashTopicsGet200ResponseTopicsInner }">
          <RouterLink class="underline" :to="{ name: RouteName.TopicPublished }">
            <Tag :severity="slotProps.data.published_count === 0 ? 'secondary' : 'warn'">{{
              slotProps.data.published_count
            }}</Tag>
          </RouterLink>
        </template>
      </Column>
      <Column header="操作">
        <template #body="slotProps: { data: DashTopicsGet200ResponseTopicsInner }">
          <div class="space-x-2">
            <Button
              rounded
              size="small"
              icon="pi pi-pencil"
              @click="handleClickEditing(slotProps.data)"
            ></Button>
            <Button
              icon="pi pi-trash"
              severity="danger"
              rounded
              size="small"
              @click="
                () => {
                  confirm.require({
                    message: '确定要删除吗？',
                    accept: () => {
                      api.dashTopicsTopicIdDelete(slotProps.data.id!).then(() => {
                        updateViewData()
                      })
                    },
                  })
                }
              "
            ></Button>
            <Button
              icon="pi pi-send"
              rounded
              severity="help"
              size="small"
              @click="
                () => {
                  currentOperatingTopic = slotProps.data
                  publishDialogVisible = true
                }
              "
            ></Button>
          </div>
        </template>
      </Column>
    </PagingTable>
  </ViewLayout>

  <!-- publish -->
  <Dialog
    modal
    header="发布专题"
    v-model:visible="publishDialogVisible"
    :style="{ width: '30rem' }"
  >
    <TopicPublishFormView @save="handlePublishTopic" saveLabel="发 布" saveIcon="pi pi-send" />
  </Dialog>
</template>
